import request from '@/request/index';

// 产品列表
export function getProductList() {
  return request({
    url: '/Web/MeituanCompensation/getProductList',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 合约列表/导出（is_export=1）
export function getContracts() {
  return request({
    url: '/Web/BusinessFit/contracts',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 合约详情
export function getContractsDetail() {
  return request({
    url: '/Web/BusinessFit/contractsDetail',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 暂停
export function subscriptionPause() {
  return request({
    url: '/Web/BusinessFit/subscriptionPause',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 解约-立即
export function subscriptionSurrender() {
  return request({
    url: '/Web/BusinessFit/subscriptionSurrender',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 解约-延期
export function subscriptionSurrenderDelay() {
  return request({
    url: '/Web/BusinessFit/subscriptionSurrenderDelay',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 恢复
export function subscriptionRegain() {
  return request({
    url: '/Web/BusinessFit/subscriptionRegain',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 取消解约
export function subscriptionSurrenderCancel() {
  return request({
    url: '/Web/BusinessFit/subscriptionSurrenderCancel',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 支付宝查询合约
export function getSubscriptionByZfb() {
  return request({
    url: '/Web/BusinessFit/getSubscriptionByZfb',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 获取下次计划
export function getNextPlan() {
  return request({
    url: '/Web/BusinessFit/getNextPlan',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 履约扣款记录列表/导出（is_export=1）
export function fitItemsList() {
  return request({
    url: '/Web/BusinessFit/fitItemsList',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 保存美团跑路赔产品配置
export function saveProductConfig() {
  return request({
    url: '/Web/MeituanCompensation/saveProduct',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 获取美团跑路赔产品详情
export function getCompensationDetail() {
  return request({
    url: '/Web/MeituanCompensation/getDetail',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 删除产品（支持单个或批量）
export function delProduct() {
  return request({
    url: '/Web/MeituanCompensation/delProduct',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 获取未授权场馆列表
export function getUnauthorizedList() {
  return request({
    url: '/Web/MeituanAuth/getUnauthorizedList',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 获取已授权场馆列表
export function getAuthorizedList() {
  return request({
    url: '/Web/MeituanAuth/getAuthorizedList',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 批量授权
export function batchAuthorize() {
  return request({
    url: '/Web/MeituanAuth/batchAuthorize',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

export default {
  getProductList,
  getContracts,
  getContractsDetail,
  subscriptionPause,
  subscriptionSurrender,
  subscriptionSurrenderDelay,
  subscriptionRegain,
  subscriptionSurrenderCancel,
  getSubscriptionByZfb,
  getNextPlan,
  fitItemsList,
  saveProductConfig,
  getCompensationDetail,
  delProduct,
  getUnauthorizedList,
  getAuthorizedList,
  batchAuthorize,
};
