import request from '@/request/index';

// 获取会员卡列表
export function getCardList() {
  return request({
    url: 'Web/Member/get_card_all',
    options: {
      method: 'GET',
    },
    immediate: false,
  });
}

/*
  获取会员卡列表2
  belong_bus_id
  bus_id 套餐包编辑时，被编辑套餐包的bus_id
  is_help_sale 2总部端套餐包，0门店套餐包或者不传
  type 0或空 不包含体验卡 1 包含体验卡(注意：此时需要belongBusId有值)
  member_list 传1返回的数据格式会变
*/
export function getMemberListCards(params?: any) {
  return request({
    url: 'Web/Member/get_card',
    options: {
      method: 'GET',
      params,
    },
    immediate: false,
  });
}

// 获取套餐包列表
export function getPackageList(params?: any) {
  return request({
    url: 'Web/Member/get_package',
    options: {
      method: 'GET',
      params,
    },
    immediate: false,
  });
}

/**
 * 教练列表
 * belong_bus_id
 * contain_deleted
 * type: 0所有 1私教 2泳教
 */
export function getCoachList() {
  return request({
    url: 'Web/Member/get_coach',
    options: {
      method: 'GET',
    },
    immediate: false,
  });
}

/**
 * 私教教练列表
 * belong_bus_id
 */
export function getPtCoachList() {
  return request({
    url: 'Web/Member/get_all_private_coach_list',
    options: {
      method: 'GET',
    },
    immediate: false,
  });
}

/**
 * 会籍列表
 * belong_bus_id
 */
export function getMembershipList() {
  return request({
    url: 'Web/Member/get_sale',
    options: {
      method: 'GET',
    },
    immediate: false,
  });
}

/**
 * 用户消息
 */
export function getUserMsg() {
  return request({
    url: 'Web/Member/userMsg',
    options: {
      method: 'POST',
    },
    // immediate: false,
  });
}

/**
 * 用户会员卡列表
 */
export function getCardUserList() {
  return request({
    url: 'Web/Member/carduserList',
    options: {
      method: 'POST',
    },
    // immediate: false,
  });
}

/**
 * 获取会员注销记录列表
 */
export function getCancellationList(data?: any) {
  return request({
    url: 'Web/Member/unregister_user_list',
    options: {
      method: 'POST',
      data,
    },
    immediate: false,
  });
}

export default {};
