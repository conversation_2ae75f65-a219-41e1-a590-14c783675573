<template>
  <div class="base-box">
    <a-space direction="vertical" :size="20" fill>
      <div class="space-unit">
        <a-card class="general-card" :bordered="false" :header-style="{ paddingBottom: '0' }">
          <template #title>
            <a-space>
              概况
              <a-radio-group v-model="timeSelect" type="button" size="mini" @change="onDataTypeChange">
                <a-radio value="昨日">昨日</a-radio>
                <a-radio value="本月">本月</a-radio>
              </a-radio-group>
              <a-range-picker
                v-model="dateRange"
                :disabled-date="disabledDate"
                :allow-clear="false"
                size="mini"
                style="width: 225px"
                @select="onSelect"
                @change="onRangeChange" />
            </a-space>
          </template>
          <PublicOpinion />
        </a-card>
      </div>
      <div>
        <a-grid :cols="24" :col-gap="16" :row-gap="16">
          <a-grid-item :span="8">
            <PopularAuthor title="收入流水" :type="1" />
          </a-grid-item>
          <a-grid-item :span="8">
            <PopularAuthor title="销售业绩" :type="2" />
          </a-grid-item>
          <a-grid-item :span="8">
            <PopularAuthor title="客流榜" :type="3">
              <template #title>
                <span style="margin-right: 3px; font-weight: bold">客流榜</span>
                <a-tooltip
                  content="客流数据为每日统计数据，之后对签到等动作进行取消会影响客流统计，精准数据请到客流汇总报表中进行查看">
                  <icon-question-circle style="color: #ff6323" />
                </a-tooltip>
              </template>
            </PopularAuthor>
          </a-grid-item>
        </a-grid>
      </div>
      <div>
        <a-card class="general-card" :bordered="false" :header-style="{ paddingBottom: '0' }">
          <!-- <template #title>数据总览</template> -->
          <a-tabs default-active-key="1" lazy-load>
            <a-tab-pane key="1" title="客流走势">
              <ChartAnalysis :type="1" />
            </a-tab-pane>
            <a-tab-pane key="2" title="业绩走势">
              <ChartAnalysis :type="2" />
            </a-tab-pane>
            <a-tab-pane key="3" title="流水走势">
              <ChartAnalysis :type="3" />
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </div>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import ChartAnalysis from './components/chart-analysis.vue';
  import PublicOpinion from './components/public-opinion.vue';
  import PopularAuthor from './components/popular-author.vue';

  defineOptions({
    name: 'Overview',
  });
  const dateRange = ref<any[]>(['', '']);
  const dateRangeObj = reactive({
    begin_date: dateRange.value[0],
    end_date: dateRange.value[1],
  });
  provide('dateRangeObj', dateRangeObj);
  function setRangeDate([beginDate, endDate]: string[]) {
    dateRange.value = [beginDate, endDate];
    dateRangeObj.begin_date = beginDate;
    dateRangeObj.end_date = endDate;
  }
  function calBeginDate(selectVal: number, endDayDiff?: number) {
    const beginDate = dayjs().subtract(selectVal, 'day').format('YYYY-MM-DD');
    const endDate = dayjs()
      .subtract(endDayDiff || 0, 'day')
      .format('YYYY-MM-DD');
    return [beginDate, endDate];
  }
  function onDataTypeChange(val: any) {
    let minus = 1;
    const todayDate = new Date().getDate();
    switch (val) {
      case '昨日':
        setRangeDate(calBeginDate(1, 1));
        break;
      case '本月':
        if (todayDate === 1) {
          minus = 0; // 如果今天是1号，则不减去1天
        }
        setRangeDate(calBeginDate(todayDate - 1, minus));
        break;
      default:
    }
  }
  const timeSelect = ref('本月');
  onDataTypeChange('本月');
  const selectDate = ref();
  function onRangeChange(val: any) {
    selectDate.value = null;
    setRangeDate(val);
    const valString = val.toString();
    if (valString === calBeginDate(1, 1).toString()) {
      timeSelect.value = '昨日';
    } else if (valString === calBeginDate(new Date().getDate() - 1, 1).toString()) {
      timeSelect.value = '本月';
    } else {
      timeSelect.value = '';
    }
  }
  // 选中日期发生改变但组件值未改变
  function onSelect(valueString: any, value: any) {
    selectDate.value = value;
  }

  function disabledDate(current: Date) {
    const dates = selectDate.value;
    const afterToday = dayjs(current).isAfter(dayjs().subtract(1, 'day'));
    if (dates && dates.length) {
      const maxSelectDay = 90; // 最多选择的天数
      const tooLate = dates[0] && Math.abs((+new Date(current) - +dates[0]) / (24 * 60 * 60 * 1000)) > maxSelectDay;
      const tooEarly = dates[1] && Math.abs((+new Date(current) - +dates[1]) / (24 * 60 * 60 * 1000)) > maxSelectDay;
      return tooEarly || tooLate || afterToday;
    }
    return false || afterToday;
  }
</script>

<style lang="less" scoped>
  .space-unit {
    margin-top: 20px;
  }

  :deep(.arco-tabs-tab) {
    &:first-child {
      margin-left: 0;
    }
  }
</style>
