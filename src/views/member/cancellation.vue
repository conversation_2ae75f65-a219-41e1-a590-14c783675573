<template>
  <div class="base-box">
    <Breadcrumb />

    <a-card class="general-card">
      <a-form
        :model="searchForm"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        auto-label-width>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="时间">
              <a-range-picker
                v-model="dateRange"
                style="width: 100%"
                format="YYYY-MM-DD"
                :placeholder="['开始时间', '结束时间']"
                :shortcuts="shortcuts"
                shortcuts-position="left"
                :disabled-date="disabledDate"
                :allow-clear="false"
                @change="handleDateChange" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="phone" label="手机号">
              <a-input v-model="searchForm.phone" placeholder="请输入" allow-clear @press-enter="handleSearch" />
            </a-form-item>
          </a-col>
          <a-divider style="height: 32px" direction="vertical" />
          <a-col :flex="'86px'" style="text-align: right">
            <a-form-item>
              <a-button type="primary" @click="handleSearch">
                <template #icon>
                  <icon-search />
                </template>
                搜索
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <a-divider style="margin-top: 0" />

      <a-table
        :loading="loading"
        :data="tableData"
        :pagination="pagination"
        :bordered="false"
        :size="size"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange">
        <template #columns>
          <a-table-column title="昵称" data-index="username" align="center" />
          <a-table-column title="手机号" data-index="phone" align="center">
            <!-- <template #cell="{ record }">
              {{ formatPhone(record.phone) }}
            </template> -->
          </a-table-column>
          <a-table-column title="注销时间" data-index="create_time" align="center">
            <!-- <template #cell="{ record }">
              {{ formatDateTime(record.create_time) }}
            </template> -->
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import dayjs from 'dayjs';
  import type { CalendarValue, ShortcutType } from '@arco-design/web-vue/es/date-picker/interface';
  import { IconSearch } from '@arco-design/web-vue/es/icon';
  import { Message } from '@arco-design/web-vue';
  import { getCancellationList } from '@/api/member';
  import { Pagination } from '@/types/global';

  defineOptions({
    name: 'MemberCancellation',
  });

  interface CancellationRecord {
    id: string;
    nickname: string;
    phone: string;
    cancellation_time: string;
  }

  interface SearchForm {
    phone: string;
    begin_date: string;
    end_date: string;
  }

  const loading = ref(false);
  const tableData = ref<CancellationRecord[]>([]);
  const size = ref<'medium'>('medium');

  // 设置默认1个月日期范围
  const getDefaultDateRange = (): string[] => {
    const endDate = dayjs().format('YYYY-MM-DD');
    const startDate = dayjs().subtract(1, 'month').format('YYYY-MM-DD');
    return [startDate, endDate];
  };

  const dateRange = ref<string[]>(getDefaultDateRange());
  const searchForm = reactive<SearchForm>({
    phone: '',
    begin_date: dateRange.value[0],
    end_date: dateRange.value[1],
  });

  const basePagination: Pagination = {
    current: 1,
    pageSize: 10,
  };

  const pagination = reactive({
    ...basePagination,
    showPageSize: true,
    showTotal: true,
    total: 0,
  });

  const { execute: fetchCancellationList } = getCancellationList();

  // 快速选择按钮配置
  const shortcuts = ref<ShortcutType[]>([
    {
      label: '1个月',
      value: () => [dayjs().subtract(1, 'month').toDate(), dayjs().toDate()],
    },
    {
      label: '3个月',
      value: () => [dayjs().subtract(3, 'month').toDate(), dayjs().toDate()],
    },
    {
      label: '6个月',
      value: () => [dayjs().subtract(6, 'month').toDate(), dayjs().toDate()],
    },
  ]);

  // 6个月范围验证函数
  const validateDateRange = (dates: (CalendarValue | undefined)[] | undefined): boolean => {
    if (!dates || dates.length !== 2 || !dates[0] || !dates[1]) {
      return true; // 空值或不完整的日期范围认为是有效的
    }

    const startDate = dayjs(dates[0] as string);
    const endDate = dayjs(dates[1] as string);
    const diffInMonths = endDate.diff(startDate, 'month', true);

    if (diffInMonths > 6) {
      Message.error('日期选择范围不能超过6个月');
      return false;
    }

    return true;
  };

  // 禁用日期函数 - 禁用未来日期
  const disabledDate = (current: Date): boolean => {
    return current.valueOf() > Date.now();
  };

  const fetchData = async (params?: Record<string, any>) => {
    loading.value = true;
    try {
      const postData: Record<string, any> = {
        current: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      };

      if (dateRange.value.length) {
        postData.begin_date = dateRange.value[0] || '';
        postData.end_date = dateRange.value[1] || '';
      } else {
        postData.begin_date = '';
        postData.end_date = '';
      }

      if (searchForm.phone) {
        postData.phone = searchForm.phone || '';
      } else {
        postData.phone = '';
      }

      const { data } = await fetchCancellationList({ data: postData });

      if (data.value) {
        tableData.value = data.value.list || [];
        pagination.total = data.value.count || 0;
        pagination.current = params?.current || 1;
      }
    } catch (error) {
      console.error('获取注销记录失败:', error);
      tableData.value = [];
    } finally {
      loading.value = false;
    }
  };

  const handleDateChange = (dates: (CalendarValue | undefined)[] | undefined) => {
    // 验证日期范围不超过6个月
    if (dates && dates.length === 2 && dates[0] && dates[1]) {
      if (!validateDateRange(dates)) {
        // 如果验证失败，恢复到默认1个月范围
        const defaultRange = getDefaultDateRange();
        const [defaultStart, defaultEnd] = defaultRange;
        dateRange.value = defaultRange;
        searchForm.begin_date = defaultStart;
        searchForm.end_date = defaultEnd;
        return;
      }
    }

    const [beginDate, endDate] = dates || ['', ''];
    dateRange.value = [beginDate as string, endDate as string];
    searchForm.begin_date = beginDate as string;
    searchForm.end_date = endDate as string;
  };

  const handleSearch = () => {
    pagination.current = 1;
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handlePageChange = (current: number) => {
    pagination.current = current;
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handlePageSizeChange = (pageSize: number) => {
    pagination.current = 1;
    pagination.pageSize = pageSize;
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // const formatPhone = (phone: string) => {
  //   if (!phone) return '';
  //   if (phone.length === 11) {
  //     return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  //   }
  //   return phone;
  // };

  // const formatDateTime = (dateTime: string) => {
  //   if (!dateTime) return '';
  //   return dateTime;
  // };

  onMounted(() => {
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  });
</script>

<style scoped lang="less">
  .base-box {
    padding: 0 20px 20px 20px;
  }

  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }
</style>
