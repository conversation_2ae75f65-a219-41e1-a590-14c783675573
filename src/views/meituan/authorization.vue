<template>
  <div class="container">
    <Breadcrumb />
    <a-card class="general-card">
      <!-- 搜索表单 - 位于tabs外部 -->
      <div class="search-section">
        <div class="search-form">
          <div class="search-row">
            <div class="search-item">
              <span class="label">场馆</span>
              <BusSelectAdmin v-model="searchParam.bus_id" style="width: 400px" placeholder="请选择场馆" />
            </div>
            <div class="search-item">
              <a-button type="primary" @click="handleSearch">
                <template #icon>
                  <icon-search />
                </template>
                搜索
              </a-button>
              <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 标签页 -->
      <a-tabs v-model:active-key="activeTab" @change="handleTabChange">
        <!-- 未授权标签页 -->
        <a-tab-pane key="unauthorized" title="未授权">
          <div class="tab-content">
            <!-- 批量操作按钮 -->
            <div class="batch-actions" style="margin-bottom: 16px">
              <a-button
                type="primary"
                :disabled="!selectedKeys.length"
                :loading="isBatchAuthLoading"
                @click="handleBatchAuthorize">
                批量授权
              </a-button>
              <span v-if="selectedKeys.length" style="margin-left: 8px; color: #666">
                已选择 {{ selectedKeys.length }} 项
              </span>
            </div>

            <!-- 未授权场馆表格 -->
            <a-table
              v-bind="unauthorizedTableProps"
              v-model:selectedKeys="selectedKeys"
              row-key="id"
              :row-selection="{
                type: 'checkbox',
                showCheckedAll: true,
                onlyCurrent: false,
              }"
              v-on="unauthorizedTableEvent"
              @selection-change="handleSelectionChange">
              <template #columns>
                <a-table-column title="场馆名称" data-index="venue_name" />
              </template>
            </a-table>

            <!-- 底部全选和批量授权 -->
            <div
              class="table-footer"
              style="margin-top: 16px; display: flex; align-items: center; justify-content: space-between">
              <div class="left-actions">
                <a-checkbox v-model="selectAll" :indeterminate="indeterminate" @change="handleSelectAll">
                  全选
                </a-checkbox>
                <a-button
                  type="primary"
                  style="margin-left: 16px"
                  :disabled="!selectedKeys.length"
                  :loading="isBatchAuthLoading"
                  @click="handleBatchAuthorize">
                  批量授权
                </a-button>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 已授权标签页 -->
        <a-tab-pane key="authorized" title="已授权">
          <div class="tab-content">
            <!-- 已授权场馆表格 -->
            <a-table v-bind="authorizedTableProps" v-on="authorizedTableEvent">
              <template #columns>
                <a-table-column title="序号" data-index="index" :width="80">
                  <template #cell="{ rowIndex }">
                    {{
                      rowIndex +
                      1 +
                      ((authorizedTableProps.pagination?.current || 1) - 1) *
                        (authorizedTableProps.pagination?.pageSize || 10)
                    }}
                  </template>
                </a-table-column>
                <a-table-column title="场馆名称" data-index="venue_name" />
                <a-table-column title="美团授权状态" data-index="auth_status">
                  <template #cell="{ record }">
                    <a-tag color="green">{{ record.auth_status || '已授权' }}</a-tag>
                  </template>
                </a-table-column>
                <a-table-column title="操作时间" data-index="operate_time" />
              </template>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { Message, Modal } from '@arco-design/web-vue';
  import { getUnauthorizedList, getAuthorizedList, batchAuthorize } from '@/api/meituan';
  import useTableProps from '@/hooks/table-props';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';

  defineOptions({
    name: 'MeituanAuthorization',
  });

  // 当前活跃的标签页
  const activeTab = ref<string>('unauthorized');

  // 选中的行
  const selectedKeys = ref<string[]>([]);

  // 批量授权加载状态
  const isBatchAuthLoading = ref(false);

  // 未授权场馆表格配置
  const {
    setSearchParam: setUnauthorizedSearchParam,
    searchParam: unauthorizedSearchParam,
    tableProps: unauthorizedTableProps,
    tableEvent: unauthorizedTableEvent,
    handleSearch: searchUnauthorized,
    loadTableList: loadUnauthorizedList,
  } = useTableProps(getUnauthorizedList, (list: Record<string, any>[]) => {
    return list.map((item: Record<string, any>) => {
      return {
        ...item,
        id: item.venue_id || item.id, // 确保有唯一ID
      };
    });
  });

  // 已授权场馆表格配置
  const {
    setSearchParam: setAuthorizedSearchParam,
    searchParam: authorizedSearchParam,
    tableProps: authorizedTableProps,
    tableEvent: authorizedTableEvent,
    handleSearch: searchAuthorized,
    loadTableList: loadAuthorizedList,
  } = useTableProps(getAuthorizedList, (list: Record<string, any>[]) => {
    return list.map((item: Record<string, any>) => {
      return {
        ...item,
        id: item.venue_id || item.id, // 确保有唯一ID
      };
    });
  });

  // 统一的搜索参数
  const searchParam = reactive({
    bus_id: '',
  });

  // 设置初始搜索参数
  setUnauthorizedSearchParam({
    bus_id: '',
  });

  setAuthorizedSearchParam({
    bus_id: '',
  });

  // 全选状态
  const selectAll = computed({
    get: () => {
      const currentData = unauthorizedTableProps.value.data || [];
      return currentData.length > 0 && selectedKeys.value.length === currentData.length;
    },
    set: (value: boolean) => {
      if (value) {
        selectedKeys.value = (unauthorizedTableProps.value.data || []).map((item: any) => item.id);
      } else {
        selectedKeys.value = [];
      }
    },
  });

  // 半选状态
  const indeterminate = computed(() => {
    const currentData = unauthorizedTableProps.value.data || [];
    return selectedKeys.value.length > 0 && selectedKeys.value.length < currentData.length;
  });

  // 搜索处理
  const handleSearch = () => {
    // 同步搜索参数到两个表格
    unauthorizedSearchParam.bus_id = searchParam.bus_id;
    authorizedSearchParam.bus_id = searchParam.bus_id;

    // 根据当前标签页执行相应的搜索
    if (activeTab.value === 'unauthorized') {
      searchUnauthorized();
    } else {
      searchAuthorized();
    }

    // 清空选中状态
    selectedKeys.value = [];
  };

  // 重置处理
  const handleReset = () => {
    searchParam.bus_id = '';
    unauthorizedSearchParam.bus_id = '';
    authorizedSearchParam.bus_id = '';

    // 根据当前标签页执行相应的搜索
    if (activeTab.value === 'unauthorized') {
      searchUnauthorized();
    } else {
      searchAuthorized();
    }

    // 清空选中状态
    selectedKeys.value = [];
  };

  // 标签页切换处理
  const handleTabChange = (key: string | number) => {
    activeTab.value = String(key);
    selectedKeys.value = []; // 切换标签页时清空选中状态

    // 根据切换到的标签页加载相应数据
    if (key === 'unauthorized') {
      loadUnauthorizedList();
    } else {
      loadAuthorizedList();
    }
  };

  // 选择变化处理
  const handleSelectionChange = (rowKeys: (string | number)[]) => {
    selectedKeys.value = rowKeys.map((key) => String(key));
  };

  // 全选处理
  const handleSelectAll = (checked: boolean | (string | number | boolean)[]) => {
    const isChecked = Array.isArray(checked) ? checked.length > 0 : checked;
    if (isChecked) {
      selectedKeys.value = (unauthorizedTableProps.value.data || []).map((item: any) => item.id);
    } else {
      selectedKeys.value = [];
    }
  };

  // 批量授权处理
  const handleBatchAuthorize = () => {
    if (!selectedKeys.value.length) {
      Message.error('请先选择需要授权的场馆');
      return;
    }

    Modal.confirm({
      title: '批量授权确认',
      content: `确定要对选中的 ${selectedKeys.value.length} 个场馆进行美团授权吗？`,
      onOk: async () => {
        isBatchAuthLoading.value = true;
        try {
          const { execute } = batchAuthorize();
          await execute({
            data: {
              venue_ids: selectedKeys.value,
              bus_id: searchParam.bus_id,
            },
          });

          Message.success('批量授权成功');
          selectedKeys.value = [];

          // 刷新未授权列表
          loadUnauthorizedList();

          // 如果当前在已授权标签页，也刷新已授权列表
          if (activeTab.value === 'authorized') {
            loadAuthorizedList();
          }
        } catch (error) {
          Message.error('批量授权失败，请重试');
        } finally {
          isBatchAuthLoading.value = false;
        }
      },
    });
  };

  // 初始化加载数据
  onMounted(() => {
    loadUnauthorizedList();
  });
</script>

<style lang="less" scoped>
  .container {
    padding: 0 20px;
  }

  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }

  .search-form {
    .search-row {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-wrap: wrap;
    }

    .search-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .label {
        font-weight: 500;
        color: #333;
        white-space: nowrap;
      }
    }
  }

  .tab-content {
    padding-top: 16px;
  }

  .batch-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .table-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e5e6eb;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  :deep(.arco-table-th) {
    background-color: #f7f8fa;
    font-weight: 600;
  }

  :deep(.arco-tabs-content) {
    padding-top: 0;
  }

  :deep(.arco-tabs-nav-tab) {
    font-weight: 500;
  }

  :deep(.arco-tabs-nav-tab-active) {
    font-weight: 600;
  }
</style>
