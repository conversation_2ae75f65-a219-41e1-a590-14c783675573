<template>
  <div class="container">
    <Breadcrumb />
    <a-card class="general-card">
      <a-tabs v-model:active-key="tabName">
        <a-tab-pane v-for="(item, index) in tabList" :key="item">
          <template #title>
            <a-badge :count="valueList[index]" :max-count="999" :offset="[-10, 0]">
              <div style="width: 100px; text-align: center">{{ labelList[index] }}</div>
            </a-badge>
          </template>
          <TheSearchPanel
            :category="item"
            :lazy="index > 0"
            :tab-name="tabName"
            :tab-list="tabList"
            @emit-count="setCount" />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { useRoute } from 'vue-router';
  import TheSearchPanel from './components/TheMonthlySearchPanel.vue';

  const tabName = ref<string>('ALL');
  const tabList = ['ALL', 'NORMAL', 'SURRENDER', 'END'];
  const labelList = ['全部', '履约中', '已解约', '已完成'];
  const valueList = ref<number[]>([0, 0, 0, 0]);

  function setCount(count: number, category: string) {
    const idx = tabList.indexOf(category);
    if (idx > -1) valueList.value[idx] = count;
  }

  const route = useRoute();
  onActivated(() => {
    const category = route.query?.category as string;
    if (category && tabList.includes(category)) {
      tabName.value = category;
    }
  });
</script>

<style lang="less" scoped>
  .container {
    padding: 0 20px 20px 20px;
  }
</style>
