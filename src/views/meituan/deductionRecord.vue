<template>
  <div class="container">
    <Breadcrumb />
    <a-card class="general-card">
      <div class="search-panel">
        <a-card>
          <div class="search-line">
            <!-- <div class="search-item">
              <div class="label">场馆</div>
              <BusSelectAdmin
                v-model="searchParams.bus_id"
                class="value"
                placeholder="请选择场馆"
                @change="onBusChange" />
            </div> -->
            <div class="search-item">
              <div class="label">美团订单编号</div>
              <a-input
                v-model="searchParams.subscription_no"
                class="value"
                placeholder="请输入美团订单编号"
                allow-clear />
            </div>
            <div class="search-item">
              <div class="label">合约编号</div>
              <a-input v-model="searchParams.subscription_no" class="value" placeholder="请输入合约编号" allow-clear />
            </div>
            <div class="search-item">
              <div class="label">商品名称</div>
              <a-input v-model="searchParams.subscription_no" class="value" placeholder="请输入商品名称" allow-clear />
            </div>
            <div class="search-item">
              <div class="label">签约会员</div>
              <a-input v-model="searchParams.keyword" class="value" placeholder="请输入会员姓名" allow-clear />
            </div>
            <div class="search-item">
              <div class="label">会员联系方式</div>
              <a-input v-model="searchParams.keyword" class="value" placeholder="请输入会员联系方式" allow-clear />
            </div>
            <div v-if="searchParams.bus_id" class="search-item">
              <div class="label">业绩归属</div>
              <SalesSelect
                v-model="searchParams.belong_id"
                placeholder="选择销售人员"
                is-coach
                style="width: 200px; margin-left: 10px"
                :belong-bus-id="searchParams.bus_id" />
            </div>
            <div class="search-item">
              <div class="label">扣款时间</div>
              <a-range-picker v-model="daterange" style="width: 300px; margin-left: 10px" format="YYYY-MM-DD" />
            </div>
            <!-- <div class="search-item">
              <div class="label">计划扣款时间</div>
              <a-range-picker v-model="planDaterange" class="value" format="YYYY-MM-DD" />
            </div>
            <div class="search-item">
              <div class="label">实际扣款时间</div>
              <a-range-picker v-model="realDaterange" class="value" format="YYYY-MM-DD" />
            </div> -->
            <div class="search-item">
              <div class="label">扣款状态</div>
              <a-select
                v-model="statusSelected"
                style="width: 200px; margin-left: 10px"
                placeholder="请选择扣款状态"
                multiple
                allow-clear>
                <a-option v-for="item in statusList" :key="item.value" :value="item.value">{{ item.label }}</a-option>
              </a-select>
            </div>
            <div class="search-actions">
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button @click="handleReset">重置</a-button>
            </div>
          </div>
        </a-card>
        <!-- <div class="panel-box">
          <div v-for="(item, index) in panelList" :key="index" class="panel-item">
            <a-card>
              <div class="value">{{ item.value }}</div>
              <div class="label">{{ item.label }}</div>
            </a-card>
          </div>
        </div> -->
        <a-card style="margin-top: 20px">
          <!-- <div style="margin-bottom: 10px; display: flex; justify-content: space-between">
            <div></div>
            <ExportExcel>
              <template #default="{ handleExport }">
                <a-button @click="() => handleClickExport(handleExport)">导出</a-button>
              </template>
            </ExportExcel>
          </div> -->
          <a-table
            :data="list"
            row-key="deduction_order_id"
            :pagination="tablePagination"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange">
            <template #columns>
              <a-table-column title="序号" data-index="index">
                <template #cell="{ rowIndex }">
                  {{ (tablePagination.current - 1) * tablePagination.pageSize + rowIndex + 1 }}
                </template>
              </a-table-column>
              <a-table-column title="美团订单编号" data-index="subscription_no">
                <template #cell="{ record }">
                  <a-link @click="openDetail(record)">{{ record.subscription_no }}</a-link>
                </template>
              </a-table-column>
              <a-table-column title="合约编号" data-index="subscription_no">
                <template #cell="{ record }">
                  <a-link @click="openDetail(record)">{{ record.subscription_no }}</a-link>
                </template>
              </a-table-column>
              <a-table-column title="商品名称" data-index="deduction_order_id" />
              <a-table-column title="美团跑路赔关联" data-index="bus_name" />
              <a-table-column title="签约会员" data-index="username" />
              <a-table-column title="会员联系方式" data-index="phone" />
              <a-table-column title="美团费率(%)" data-index="card_name" />
              <a-table-column title="业绩归属" data-index="sale_name" />
              <a-table-column title="已扣款期数">
                <template #cell="{ record }">{{ record.period }}/{{ record.periods }}</template>
              </a-table-column>
              <a-table-column title="扣款金额(￥)" align="right">
                <template #cell="{ record }">{{ toFixed2(record.deduction_amount) }}</template>
              </a-table-column>
              <a-table-column title="扣款时间" data-index="plan_deduction_time" />
              <a-table-column title="实际收入金额(￥)" align="right">
                <template #cell="{ record }">{{ toFixed2(record.deduction_amount) }}</template>
              </a-table-column>
              <!-- <a-table-column title="实际扣款时间" data-index="actual_deduction_time" /> -->
              <a-table-column title="扣款状态">
                <template #cell="{ record }">
                  <span :style="{ color: statusList.find((i) => i.value === record.status)?.color || '' }">
                    {{ statusList.find((i) => i.value === record.status)?.label || '-' }}
                  </span>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </a-card>
      </div>
    </a-card>

    <TheDetailModal v-model:showModal="detailModal" :params="detailParams" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { useBusInfoStore } from '@/store';
  // import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import SalesSelect from '@/components/membership/salesSelect.vue';
  // import ExportExcel from '@/components/exportExcel.vue';
  import { fitItemsList } from '@/api/meituan';
  import TheDetailModal from './components/TheDetailModal.vue';

  const NONE_SEARCH_PARAMS = {
    bus_id: '',
    subscription_no: '',
    keyword: '',
    belong_id: '',
    sign_time_begin: '',
    sign_time_end: '',
    actual_deduction_time_begin: '',
    actual_deduction_time_end: '',
    status: '',
    plan_deduction_time_begin: '',
    plan_deduction_time_end: '',
    page_no: 1,
    page_size: 10,
  };

  const daterange = ref<any[]>([]);
  const planDaterange = ref<any[]>([]);
  const realDaterange = ref<any[]>([]);
  const statusSelected = ref<string[]>([]);
  const searchParams = reactive({ ...NONE_SEARCH_PARAMS });
  const list = ref<any[]>([]);
  const total = ref(0);

  const statusList = [
    { value: 'WAIT_PAY', label: '待付款', color: 'gray' },
    { value: 'PAUSED', label: '暂停', color: 'grey' },
    { value: 'PAID', label: '支付成功', color: 'green' },
    { value: 'PAY_FAILED', label: '扣款失败', color: 'red' },
    { value: 'CANCEL', label: '已取消', color: 'gray' },
  ];

  const panelList = reactive<any[]>([
    { label: '待付款总金额', code: 'wait', value: 0 },
    { label: '支付成功总金额', code: 'paid', value: 0 },
    { label: '扣款失败总金额', code: 'fail', value: 0 },
    { label: '合约暂停总金额', code: 'pause', value: 0 },
    { label: '合约取消总金额', code: 'cancel', value: 0 },
  ]);

  const detailModal = ref(false);
  const detailParams = reactive({ bus_id: '', subscription_no: '' });

  const tablePagination = computed(() => ({
    current: searchParams.page_no,
    pageSize: searchParams.page_size,
    total: total.value,
    showTotal: true,
    showPageSize: true,
  }));

  function toFixed2(v: any) {
    const n = Number(v || 0);
    return n.toFixed(2);
  }

  function formatDate2(d: any) {
    const day =
      (d as any)?._isAMomentObject || d?.format ? new Date(d.toDate?.() || d) : typeof d === 'string' ? new Date(d) : d;
    const y = day.getFullYear();
    const m = `${day.getMonth() + 1}`.padStart(2, '0');
    const dd = `${day.getDate()}`.padStart(2, '0');
    return `${y}-${m}-${dd}`;
  }

  function applyRange(range: any[], beginKey: keyof typeof searchParams, endKey: keyof typeof searchParams) {
    if (Array.isArray(range) && range.length === 2 && range[0] && range[1]) {
      (searchParams as any)[beginKey] = formatDate2(range[0]);
      (searchParams as any)[endKey] = formatDate2(range[1]);
    } else {
      (searchParams as any)[beginKey] = '';
      (searchParams as any)[endKey] = '';
    }
  }

  async function getList() {
    list.value = [];
    total.value = 0;
    const panel = { cancel: 0, fail: 0, paid: 0, pause: 0, wait: 0 };
    const { data } = await fitItemsList().execute({ data: searchParams });
    const value = data.value as any;
    if (value) {
      list.value = Array.isArray(value.list) ? value.list : [];
      total.value = Number(value.count || 0);
      const sum = { ...panel, ...(value.sum || {}) };
      panelList.forEach((item) => {
        item.value = Number((sum as any)[item.code] || 0).toFixed(2);
      });
    } else {
      Message.error('请求失败，请重试');
    }
  }

  function setSearchDateRange() {
    const today = new Date();
    const firstDate = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    planDaterange.value = [firstDate, lastDate];
  }

  function handleSearch() {
    searchParams.page_no = 1;
    applyRange(daterange.value, 'sign_time_begin', 'sign_time_end');
    applyRange(planDaterange.value, 'plan_deduction_time_begin', 'plan_deduction_time_end');
    applyRange(realDaterange.value, 'actual_deduction_time_begin', 'actual_deduction_time_end');
    searchParams.status = statusSelected.value && statusSelected.value.length > 0 ? statusSelected.value.join(',') : '';
    getList();
  }

  function handleReset() {
    Object.assign(searchParams, { ...NONE_SEARCH_PARAMS });
    const busStore = useBusInfoStore();
    searchParams.bus_id = (busStore.busInfo as any)?.bus_id || '';
    daterange.value = [];
    planDaterange.value = [];
    realDaterange.value = [];
    statusSelected.value = [];
    setSearchDateRange();
    handleSearch();
  }

  function handlePageChange(page: number) {
    searchParams.page_no = page;
    getList();
  }
  function handlePageSizeChange(pageSize: number) {
    searchParams.page_size = pageSize;
    getList();
  }

  function openDetail(record: any) {
    detailParams.bus_id = searchParams.bus_id;
    detailParams.subscription_no = record.subscription_no;
    detailModal.value = true;
  }

  // function onBusChange() {
  //   searchParams.belong_id = '';
  // }

  const route = useRoute();

  // function buildExportColumns() {
  //   return [
  //     { title: '合约编号', dataIndex: 'subscription_no' },
  //     { title: '支付宝订单', dataIndex: 'deduction_order_id' },
  //     { title: '场馆', dataIndex: 'bus_name' },
  //     { title: '签约会员', dataIndex: 'username' },
  //     { title: '合约卡种', dataIndex: 'card_name' },
  //     { title: '手机号', dataIndex: 'phone' },
  //     { title: '业绩归属', dataIndex: 'sale_name' },
  //     { title: '扣款期数', dataIndex: 'period_div' },
  //     { title: '计划扣款日期', dataIndex: 'plan_deduction_time' },
  //     { title: '实际扣款时间', dataIndex: 'actual_deduction_time' },
  //     { title: '扣款金额', dataIndex: 'deduction_amount' },
  //     { title: '扣款状态', dataIndex: 'status_name' },
  //   ];
  // }

  // function formatDate(d: Date) {
  //   const y = d.getFullYear();
  //   const m = `${d.getMonth() + 1}`.padStart(2, '0');
  //   const dd = `${d.getDate()}`.padStart(2, '0');
  //   return `${y}-${m}-${dd}`;
  // }

  // async function handleClickExport(cb: any) {
  //   const params = { ...searchParams, is_export: 1, page_no: 1, page_size: total.value };
  //   const { data } = await fitItemsList().execute({ data: params });
  //   const value = data.value as any;
  //   let exportList: any[] = [];
  //   if (Array.isArray(value?.list)) {
  //     exportList = value.list;
  //     exportList.forEach((row: any) => {
  //       row.subscription_no = `\u0009${row.subscription_no}`;
  //       row.phone = `\u0009${row.phone}`;
  //       row.period_div = `(${row.period}/${row.periods})`;
  //       const item = statusList.find((it) => it.value === row.status);
  //       row.status_name = item ? item.label : '-';
  //       if (typeof row.sale_name === 'string') row.sale_name = row.sale_name.replace(',', ' ');
  //     });
  //   }
  //   cb({ filename: `履约扣款记录-${formatDate(new Date())}`, columns: buildExportColumns(), data: exportList });
  // }

  onMounted(() => {
    const { busId, subscriptionNo, date, status } = route.query as any;
    const busStore = useBusInfoStore();
    if (busId) {
      searchParams.bus_id = busId;
    } else {
      setSearchDateRange();
      searchParams.bus_id = (busStore.busInfo as any)?.bus_id || '';
    }
    if (subscriptionNo) searchParams.subscription_no = subscriptionNo;
    if (date) {
      const dateObj = new Date(date);
      const firstDate = new Date(dateObj.getFullYear(), dateObj.getMonth(), 1);
      const lastDate = new Date(dateObj.getFullYear(), dateObj.getMonth() + 1, 0);
      daterange.value = [firstDate, lastDate];
    }
    if (status) statusSelected.value = [status];
    handleSearch();
  });
</script>

<style lang="less" scoped>
  .wrap-line {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
  }

  .container {
    padding: 0 20px 20px 20px;

    .search-panel {
      padding: 0;

      .search-line {
        .wrap-line;

        .search-item {
          .wrap-line;
          margin: 10px;

          .value {
            margin-left: 10px;
            width: 200px;
          }
        }

        .search-actions {
          display: flex;
          gap: 12px;
          margin-left: auto;
        }
      }

      .panel-box {
        .wrap-line;
        padding-top: 20px;

        .panel-item {
          margin-right: 20px;

          .value {
            font-size: 30px;
            font-weight: bold;
            color: #333;
            text-align: center;
          }

          .label {
            font-size: 16px;
            color: #666;
          }
        }
      }
    }
  }
</style>
