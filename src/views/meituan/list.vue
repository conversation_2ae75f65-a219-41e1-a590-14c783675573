<template>
  <div class="container">
    <Breadcrumb />
    <a-card class="general-card">
      <!-- 搜索表单 -->
      <div class="search-section">
        <div class="search-form">
          <div class="search-row">
            <div class="search-item">
              <span class="label">商品</span>
              <a-input v-model="searchParam.goods_name" placeholder="请输入商品名称" allow-clear style="width: 200px" />
            </div>
            <div class="search-item">
              <span class="label">场馆</span>
              <BusSelectAdmin v-model="searchParam.bus_id" style="width: 400px" placeholder="请选择场馆" />
            </div>
            <div class="search-item">
              <span class="label">操作人</span>
              <a-input
                v-model="searchParam.operate_name"
                placeholder="请输入操作人姓名"
                allow-clear
                style="width: 200px" />
            </div>
            <div class="search-item">
              <span class="label">操作时间</span>
              <a-range-picker v-model="dateRange" format="YYYY-MM-DD" style="width: 240px" />
            </div>
          </div>
          <div class="search-actions">
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset">重置</a-button>
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <a-button type="primary" @click="handleAdd">新增关联</a-button>
        <a-button @click="handleBatchDelete">批量删除</a-button>
      </div>

      <!-- 数据表格 -->
      <a-table
        v-bind="tableProps"
        v-model:selected-keys="selectedKeys"
        :row-selection="{ type: 'checkbox' }"
        row-key="id"
        v-on="tableEvent">
        <template #columns>
          <a-table-column title="类型" data-index="card_type_id" />
          <a-table-column title="商品" data-index="card_name" />
          <a-table-column title="美团" data-index="deal_id">
            <template #cell="{ record }">
              <a-link>关联ID：{{ record.deal_id }}</a-link>
            </template>
          </a-table-column>
          <a-table-column title="场馆" data-index="bus_name" />
          <a-table-column title="操作人" data-index="realname" />
          <a-table-column title="操作时间" data-index="updated_at" />
          <a-table-column title="操作" :width="200" fixed="right">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="handleEdit(record)">编辑</a-link>
                <a-button type="text" status="danger" size="small" @click="handleDelete(record)">删除</a-button>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router';
  import { Message, Modal } from '@arco-design/web-vue';
  import { getProductList, delProduct } from '@/api/meituan';
  import useTableProps from '@/hooks/table-props';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';

  defineOptions({
    name: 'MeituanList',
  });

  const router = useRouter();

  // 日期范围
  const dateRange = ref<string[]>([]);

  // 选中的行
  const selectedKeys = ref<string[]>([]);

  // 使用表格 hook
  const {
    setSearchParam,
    searchParam,
    tableProps,
    tableEvent,
    handleSearch: search,
    loadTableList,
  } = useTableProps(getProductList, (list: Record<string, any>[]) => {
    return list.map((item: Record<string, any>) => {
      return {
        ...item,
      };
    });
  });

  // 搜索参数
  setSearchParam({
    goods_name: '',
    operate_name: '',
    bus_id: '',
    begin_date: '',
    end_date: '',
  });

  // 处理搜索
  const handleSearch = () => {
    // 处理日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      searchParam.begin_date = dateRange.value[0] || '';
      searchParam.end_date = dateRange.value[1] || '';
    } else {
      searchParam.begin_date = '';
      searchParam.end_date = '';
    }

    search();
  };

  // 重置搜索
  const handleReset = () => {
    Object.keys(searchParam).forEach((key) => {
      searchParam[key as keyof typeof searchParam] = '';
    });
    dateRange.value = [];
    handleSearch();
  };

  // 新增
  const handleAdd = () => {
    router.push('/meituan/save');
  };

  // 编辑
  const handleEdit = (record: any) => {
    router.push(`/meituan/save/${record.id}`);
  };

  // 删除
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确认操作',
      content: `确定要删除该商品吗？`,
      onOk: async () => {
        try {
          const ids = [record.id];
          await delProduct().execute({ data: { ids } });
          Message.success('删除成功');
          loadTableList();
        } catch (error) {
          Message.error(`删除失败`);
        }
      },
    });
  };

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedKeys.value.length === 0) {
      Message.warning('请选择要删除的商品');
      return;
    }

    Modal.confirm({
      title: '确认操作',
      content: `确定要批量删除选中的 ${selectedKeys.value.length} 个商品吗？`,
      onOk: async () => {
        try {
          const ids = selectedKeys.value;
          await delProduct().execute({ data: { ids } });
          Message.success('批量删除成功');
          selectedKeys.value = [];
          loadTableList();
        } catch (error) {
          Message.error('批量删除失败');
        }
      },
    });
  };

  // 初始化
  onMounted(() => {
    loadTableList();
  });
</script>

<style lang="less" scoped>
  .container {
    padding: 0 20px 20px 20px;
  }

  .search-section {
    margin-bottom: 16px;
  }

  .search-form {
    display: flex;

    .search-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 16px;
    }

    .search-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .label {
        white-space: nowrap;
        color: #666;
        font-size: 14px;
      }
    }

    .search-actions {
      display: flex;
      gap: 12px;
      margin-left: auto;
    }
  }

  .action-section {
    margin-bottom: 16px;
    display: flex;
    gap: 12px;
  }

  :deep(.arco-table-th) {
    background-color: #fafafa;
  }

  :deep(.arco-table-td) {
    border-bottom: 1px solid #f0f0f0;
  }
</style>
