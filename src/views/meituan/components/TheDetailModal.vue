<template>
  <a-modal v-model:visible="isShowModal" title="详情" :width="1000" :footer="false" @before-close="handleClose">
    <div v-if="detailData" class="detail-content">
      <!-- 基本信息区域 -->
      <div class="info-section">
        <div class="info-row">
          <div class="info-item">
            <span class="label">美团核销码：</span>
            <span class="value">{{ detailData.verification_code || detailData.subscription_no }}</span>
          </div>
          <div class="info-item">
            <span class="label">商品名称：</span>
            <span class="value">{{ detailData.product_title || detailData.product_name }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">美团跑路赔：</span>
            <span class="value">{{ detailData.product_type === 2 ? '组合' : '常规' }}</span>
          </div>
          <div class="info-item">
            <span class="label">签约金额(￥)：</span>
            <span class="value">{{ formatAmount(detailData.sign_amount) }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">会员：</span>
            <span class="value">{{ detailData.username }}</span>
          </div>
          <div class="info-item">
            <span class="label">会员联系方式：</span>
            <span class="value">{{ detailData.phone }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">最近一期扣款金额(￥)：</span>
            <span class="value">{{ formatAmount(detailData.latest_deduction_amount) }}</span>
          </div>
          <div class="info-item">
            <span class="label">最近一期扣款时间：</span>
            <span class="value">{{ detailData.latest_deduction_time }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">成功扣款期数：</span>
            <span class="value">{{ detailData.success_periods || 0 }}/{{ detailData.total_periods || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="label">合约状态：</span>
            <span class="value">{{ getStatusText(detailData.status) }}</span>
          </div>
        </div>
      </div>

      <!-- 分期详情区域 -->
      <div class="installment-section">
        <div class="installment-header">
          <div class="installment-info">
            <span class="period-count">共{{ detailData.total_periods || 0 }}期</span>
            <span class="period-note">这个期数是根据合约中的有效期的，半年为6期，年卡为12期</span>
          </div>
        </div>

        <div class="installment-list">
          <div
            v-for="(item, index) in installmentList"
            :key="index"
            class="installment-item"
            :class="{ failed: item.status === 'failed', pending: item.status === 'pending' }">
            <div class="period-info">
              <span class="period-label">第{{ index + 1 }}期：</span>
              <span class="amount" :class="{ 'failed-amount': item.status === 'failed' }">
                {{ item.status === 'failed' ? '扣款失败' : `扣款金额￥${formatAmount(item.amount)}` }}
              </span>
            </div>
            <div class="time-info" :class="{ 'failed-time': item.status === 'failed' }">
              扣款时间{{ formatTime(item.time) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="loading-content">
      <a-spin :loading="loading" />
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { getContractsDetail } from '@/api/meituan';

  interface InstallmentItem {
    amount: number;
    time: string;
    status: 'success' | 'failed' | 'pending';
  }

  interface DetailData {
    verification_code?: string;
    subscription_no?: string;
    product_title?: string;
    product_name?: string;
    product_type?: number;
    sign_amount?: number;
    username?: string;
    phone?: string;
    latest_deduction_amount?: number;
    latest_deduction_time?: string;
    success_periods?: number;
    total_periods?: number;
    status?: string;
    installments?: InstallmentItem[];
    [key: string]: any;
  }

  interface Props {
    showModal?: boolean;
    params?: {
      bus_id?: string;
      subscription_no?: string;
      out_subscription_no?: string;
      card_type?: string;
      [key: string]: any;
    };
  }

  const props = withDefaults(defineProps<Props>(), {
    showModal: false,
    params: () => ({}),
  });

  const emit = defineEmits<{
    'update:showModal': [value: boolean];
  }>();

  const loading = ref(false);
  const detailData = ref<DetailData | null>(null);

  const isShowModal = computed({
    get: () => props.showModal,
    set: (value: boolean) => {
      emit('update:showModal', value);
    },
  });

  // 模拟分期数据，实际应该从API获取
  const installmentList = computed(() => {
    if (!detailData.value) return [];

    const totalPeriods = detailData.value.total_periods || 3;
    const successPeriods = detailData.value.success_periods || 0;
    const list: InstallmentItem[] = [];

    for (let i = 0; i < totalPeriods; i += 1) {
      if (i < successPeriods) {
        // 成功扣款的期数
        list.push({
          amount: detailData.value.latest_deduction_amount || 1000,
          time: '2025.07.22 12:00',
          status: 'success',
        });
      } else if (i === successPeriods && detailData.value.status === 'FAILED') {
        // 失败的期数
        list.push({
          amount: 0,
          time: '2025.07.22 12:00',
          status: 'failed',
        });
      } else {
        // 待扣款的期数
        list.push({
          amount: detailData.value.latest_deduction_amount || 1000,
          time: '2025.07.22 12:00',
          status: 'pending',
        });
      }
    }

    return list;
  });

  const formatAmount = (amount: number | string | undefined): string => {
    if (!amount) return '0.00';
    return Number(amount).toFixed(2);
  };

  const formatTime = (time: string): string => {
    return time || '--';
  };

  const getStatusText = (status: string | undefined): string => {
    const statusMap: Record<string, string> = {
      NORMAL: '履约中/已完成',
      PAUSED: '已暂停',
      SURRENDER: '已解约',
      END: '已完成',
      WAIT_SURRENDER: '待解约',
      FAILED: '扣款失败',
    };
    return statusMap[status || ''] || '未知状态';
  };

  const fetchDetailData = async () => {
    if (!props.params.subscription_no) return;

    loading.value = true;
    try {
      // 使用 execute 方法调用 API，传入参数
      const { data } = await getContractsDetail().execute({
        data: {
          bus_id: props.params.bus_id,
          subscription_no: props.params.subscription_no,
          out_subscription_no: props.params.out_subscription_no,
          card_type: props.params.card_type,
        },
      });
      detailData.value = data.value;
    } catch (error) {
      console.error('获取详情失败:', error);
    } finally {
      loading.value = false;
    }
  };

  const handleClose = () => {
    emit('update:showModal', false);
    detailData.value = null;
  };

  watch(
    () => props.showModal,
    (newVal) => {
      if (newVal) {
        fetchDetailData();
      }
    },
    { immediate: true }
  );
</script>

<style lang="less" scoped>
  .detail-content {
    padding: 16px 0;
  }

  .loading-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .info-section {
    margin-bottom: 24px;
  }

  .info-row {
    display: flex;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-item {
    flex: 1;
    display: flex;
    align-items: center;

    .label {
      color: #666;
      margin-right: 8px;
      white-space: nowrap;
    }

    .value {
      color: #333;
      font-weight: 500;
    }
  }

  .installment-section {
    border-top: 1px solid #f0f0f0;
    padding-top: 24px;
  }

  .installment-header {
    margin-bottom: 16px;
  }

  .installment-info {
    display: flex;
    align-items: center;
    gap: 16px;

    .period-count {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .period-note {
      font-size: 12px;
      color: #999;
    }
  }

  .installment-list {
    background: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 6px;
    padding: 16px;
  }

  .installment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    &:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 8px;
      padding-bottom: 16px;
    }

    &.failed {
      .period-info .amount {
        color: #ff4d4f;
      }

      .time-info {
        color: #ff4d4f;
      }
    }

    &.pending {
      opacity: 0.7;
    }
  }

  .period-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .period-label {
      color: #666;
      font-size: 14px;
    }

    .amount {
      color: #333;
      font-weight: 500;

      &.failed-amount {
        color: #ff4d4f;
      }
    }
  }

  .time-info {
    color: #666;
    font-size: 14px;

    &.failed-time {
      color: #ff4d4f;
    }
  }
</style>
