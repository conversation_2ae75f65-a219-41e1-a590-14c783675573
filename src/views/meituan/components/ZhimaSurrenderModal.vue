<template>
  <a-modal v-model:visible="show" :mask-closable="false" :width="465" title="提示" @before-close="handleClose">
    <a-form ref="formRef" class="modal-form" :model="formData">
      <a-form-item
        field="type"
        :rules="[{ required: true, type: 'string', message: '请选择解约方式', trigger: 'change' }]">
        <a-radio-group v-model="formData.type">
          <a-radio value="1">立即执行</a-radio>
          <a-radio v-show="info && info.done_period < info.periods && lastPeriod" value="2">
            延期解约
            <span style="margin-left: 6px; font-size: 12px; color: #7f7f7f">系统会在下次扣款成功后进行解约</span>
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item>
        <div v-show="formData.type === '1'">解约后未交费的订单（含欠费订单）将不再扣费，确定解约?</div>
        <div v-show="formData.type === '2'">
          当前履约进度是
          <b>【{{ info.done_period }}/{{ info.periods }}】</b>
          期，将在
          <b style="color: #70b603">{{ plan_deduction_time }}</b>
          第
          <b>【{{ next_period }}/{{ info.periods }}】</b>
          期扣款成功后解约。
        </div>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button type="primary" @click="handleConfirm">确定</a-button>
      <a-button @click="handleClose">取消</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { getNextPlan, subscriptionSurrender, subscriptionSurrenderDelay } from '@/api/meituan';

  const props = defineProps({
    modelValue: { type: Boolean, default: false },
    info: { type: Object, required: true },
    hasNextPlan: { type: Boolean, default: false },
  });

  const emit = defineEmits(['update:modelValue', 'onSuccess']);

  const formRef = ref();
  const formData = reactive({ type: '1' });
  const lastPeriod = ref(false);
  const plan_deduction_time = ref('');
  const next_period = ref(0);

  const show = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
  });

  watch(show, async (val) => {
    if (val) {
      lastPeriod.value = false;
      if (props.hasNextPlan) {
        const { data } = await getNextPlan().execute({
          data: {
            open_merchant_id: props.info.open_merchant_id,
            subscription_no: props.info.subscription_no,
            card_type: props.info.card_type,
          },
        });
        if (data.value?.plan_deduction_time) {
          plan_deduction_time.value = data.value.plan_deduction_time;
          lastPeriod.value = true;
          next_period.value = data.value.period || props.info.done_period;
          if (Number(props.info.un_sign_type) === 1) formData.type = '1';
          else if (Number(props.info.un_sign_type) === 2) formData.type = '2';
        }
      }
    }
  });

  function handleClose() {
    formRef.value?.resetFields();
    lastPeriod.value = false;
    emit('update:modelValue', false);
  }

  async function handleConfirm() {
    const errors = await formRef.value?.validate();
    if (errors) return;
    const params = {
      open_merchant_id: props.info.open_merchant_id,
      subscription_no: props.info.subscription_no,
      card_type: props.info.card_type,
    };
    const api = formData.type === '1' ? subscriptionSurrender : subscriptionSurrenderDelay;
    const { data } = await api().execute({ data: params });
    if (data.value) {
      Message.success('操作成功');
      emit('onSuccess');
      handleClose();
    }
  }
</script>

<style lang="less" scoped></style>
