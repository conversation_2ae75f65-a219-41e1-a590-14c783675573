<template>
  <a-modal v-model:visible="show" :mask-closable="false" :width="600" title="提示" @before-close="handleClose">
    <a-form ref="formRef" class="modal-form" :model="formData" :label-col-props="{ style: { width: '180px' } }">
      <!-- 解约方式选择 -->
      <a-form-item
        field="type"
        :rules="[{ required: true, type: 'string', message: '请选择解约方式', trigger: 'change' }]">
        <a-radio-group v-model="formData.type">
          <a-radio value="1">立即执行</a-radio>
          <a-radio v-show="info && info.done_period < info.periods && lastPeriod" value="2">
            延期解约
            <span style="margin-left: 6px; font-size: 12px; color: #7f7f7f">系统会在下次扣款成功后进行解约</span>
          </a-radio>
        </a-radio-group>
      </a-form-item>

      <template v-if="formData.type === '1'">
        <a-form-item label="剩余期数">
          <span>{{ remainingPeriods }}期</span>
        </a-form-item>
        <a-form-item label="待付款金额">
          <span>{{ pendingAmount }}元</span>
        </a-form-item>
        <a-form-item label="违约金比例">
          <span>{{ penaltyRate }}%</span>
        </a-form-item>
        <a-form-item label="预估违约金">
          <a-input-number
            v-model="formData.damages_cash"
            :min="0"
            :precision="2"
            style="width: 200px; display: inline-block" />
          <span style="margin-left: 8px">元</span>
        </a-form-item>
      </template>
      <template v-else>
        <a-form-item label="预估解约时待付款金额">
          <div>{{ nextInfo?.refund_cash }}元</div>
          <em>预估解约时待付款金额=当前待付款金额-下期扣款金额</em>
        </a-form-item>
        <a-form-item label="违约金比例">
          <span>{{ nextInfo?.damages_rate }}%</span>
        </a-form-item>
        <a-form-item label="预估违约金">
          <span>{{ nextInfo?.damages_cash }}元</span>
        </a-form-item>
      </template>

      <a-form-item>
        <div v-show="formData.type === '1'">
          <span style="color: red; font-size: 14px; margin-top: 20px">*</span>
          解约后未交费的订单（含欠费订单）将不再扣费，确定解约?
        </div>
        <div v-show="formData.type === '2'">
          当前履约进度是
          <b>【{{ info.done_period }}/{{ info.periods }}】</b>
          期，将在
          <b style="color: #70b603">{{ plan_deduction_time }}</b>
          第
          <b>【{{ next_period }}/{{ info.periods }}】</b>
          期扣款成功后解约。
        </div>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button type="primary" :loading="submitting" @click="handleConfirm">确定</a-button>
      <a-button style="margin-left: 8px" @click="handleClose">取消</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { getNextPlan, subscriptionSurrender, subscriptionSurrenderDelay } from '@/api/meituan';

  const props = defineProps({
    modelValue: { type: Boolean, default: false },
    info: { type: Object, required: true },
    hasNextPlan: { type: Boolean, default: false },
  });
  const emit = defineEmits(['update:modelValue', 'onSuccess']);

  const formRef = ref();
  const formData = reactive<any>({ type: '1', damages_cash: null });
  const submitting = ref(false);
  const plan_deduction_time = ref('');
  const lastPeriod = ref(false);
  const next_period = ref(0);
  const nextInfo = ref<any>({});

  const show = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
  });

  const remainingPeriods = computed(() => props.info?.remain_count || '0');
  const pendingAmount = computed(() => props.info?.refund_cash || '0');
  const penaltyRate = computed(() => props.info?.damages_rate || '0');

  function initFormData() {
    formData.type = '1';
    formData.damages_cash = Number(props.info?.damages_cash || 0);
    lastPeriod.value = false;
  }

  watch(show, async (val) => {
    if (val) {
      initFormData();
      if (props.hasNextPlan) {
        const { data } = await getNextPlan().execute({
          data: {
            open_merchant_id: props.info.open_merchant_id,
            subscription_no: props.info.subscription_no,
            card_type: props.info.card_type,
          },
        });
        if (data.value) {
          plan_deduction_time.value = data.value.plan_deduction_time;
          lastPeriod.value = true;
          next_period.value = data.value.period || props.info.done_period;
          if (Number(props.info.un_sign_type) === 1) formData.type = '1';
          else if (Number(props.info.un_sign_type) === 2) formData.type = '2';
          nextInfo.value = data.value.damages || {};
        }
      }
    }
  });

  function handleClose() {
    formRef.value?.resetFields();
    submitting.value = false;
    emit('update:modelValue', false);
  }

  async function handleConfirm() {
    const errors = await formRef.value?.validate();
    if (errors) return;
    submitting.value = true;
    const urlApi = formData.type === '1' ? subscriptionSurrender : subscriptionSurrenderDelay;
    const params = {
      bus_id: props.info.bus_id,
      open_merchant_id: props.info.open_merchant_id,
      subscription_no: props.info.subscription_no,
      card_type: props.info.card_type,
      total_cash: pendingAmount.value,
      ...formData,
    };
    try {
      const { data } = await urlApi().execute({ data: params });
      submitting.value = false;
      if (data.value) {
        Message.success('操作成功');
        emit('onSuccess');
        handleClose();
      }
    } catch (e) {
      submitting.value = false;
      Message.error('请求失败，请重试');
    }
  }
</script>

<style lang="less" scoped>
  .modal-form {
    :deep(.arco-form-item-label) {
      font-weight: normal;
      color: #333;
    }
    :deep(.arco-form-item-content) {
      span {
        color: #333;
        font-size: 14px;
      }
    }
  }

  .modal-buttons {
    text-align: center;
  }
</style>
