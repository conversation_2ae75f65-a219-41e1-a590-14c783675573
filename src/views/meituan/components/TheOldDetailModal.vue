<template>
  <div class="detail-modal">
    <a-modal v-model:visible="detailModal" :width="'60%'" title="详情" @before-close="handleDetailClose">
      <div class="information">
        <div class="line">
          <div class="item">
            <div class="label">合约编号:</div>
            <div class="value">{{ detailData.subscription_no }} {{ statusLabel }}</div>
          </div>
        </div>
        <div v-if="detailData.status === 'PAUSED'" class="line">
          <div class="item">
            <div class="label">暂停时间:</div>
            <div class="value">{{ detailData.paused_time }}</div>
          </div>
        </div>
        <div v-if="detailData.status === 'SURRENDER'" class="line">
          <div class="item">
            <div class="label">解约时间:</div>
            <div class="value">
              {{ detailData.surrender_time }} ({{ detailData.un_sign_obj == 1 ? '商家解约' : '会员解约' }})
            </div>
          </div>
        </div>
        <div v-if="detailData.status === 'END'" class="line">
          <div class="item">
            <div class="label">完成时间:</div>
            <div class="value">{{ detailData.end_times }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">签约会员:</div>
            <div class="value">{{ detailData.username }} {{ detailData.bus_name }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">签约销售:</div>
            <div class="value">{{ detailData.sale_name }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">签约时间:</div>
            <div class="value">{{ detailData.sign_time }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">当前业绩归属:</div>
            <div class="value">{{ detailData.marketers_name }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">产品方案:</div>
            <div v-if="detailData.product_type == 1" class="value">(常规)</div>
            <div v-else-if="detailData.product_type == 2" class="value">(组合)</div>
            <div class="value">{{ detailData.product_title }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">合约方式:</div>
            <div class="value">
              ({{ detailData.business_type == 2 ? '分期付' : '月付' }})
              {{ detailData.gen_type == 0 ? '签约后生成合约' : '首期扣款后生成合约' }}
            </div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">卡种:</div>
            <span class="value">{{ detailData.card_name }}</span>
            <!-- 有效期 30天 ，购买12000.00元，赠送300.00元 -->
            <div class="value">
              （ 有效期{{ detailData?.period_day }}天，购买{{ detailData?.product_info?.purchase_volume
              }}{{ detailData?.unit }}，赠送{{ detailData?.product_info?.gift_volume }}{{ detailData?.unit }}）
            </div>
          </div>
        </div>
        <div v-if="combinationsTotal" class="line">
          <div class="item">
            <div class="label">签约付款:</div>
            <div class="value">￥{{ combinationsTotal }}</div>
          </div>
        </div>
        <a-alert v-if="detailData.product_type == 2" type="warning" style="margin: 0 40px">
          <template #desc>
            <div v-for="(item2b, index2b) in combinations" :key="'b_' + index2b" class="line">
              <div v-for="(item, index) in item2b" :key="'bb_' + index" class="item">
                <div v-if="item" class="label">{{ item.title }}:</div>
                <div v-if="item" class="value">￥{{ item.amount }}</div>
              </div>
            </div>
          </template>
        </a-alert>
        <div v-if="detailData.product_type == 1" class="line">
          <div class="item">
            <div class="label">首期付款:</div>
            <div class="value">￥{{ detailData.down_payment }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">单期付款:</div>
            <div class="value">￥{{ normalTotal }}</div>
          </div>
        </div>
        <a-alert type="warning" style="margin: 0 40px">
          <template #desc>
            <div class="line">
              <div class="item">
                <div class="label">扣款:</div>
                <div class="value">
                  单期 ￥{{ detailData.deduction_amount }}, 共
                  {{ detailData.product_type == 1 ? Number(detailData.periods) - 1 : detailData.periods }}期
                </div>
              </div>
            </div>
          </template>
        </a-alert>
        <div class="line">
          <div class="item">
            <div class="label">违约金:</div>
            <!-- customer_un_sign 
            <Radio :label="0">场馆解约</Radio>
            <Radio :label="1">会员自行解约</Radio>
            un_sign_type 
            <Radio :label="1">线下收取</Radio>
            <Radio :label="2">支付宝自动扣款</Radio> -->
            <div v-if="detailData.customer_un_sign == 1" class="value">{{ detailData.violate_amount }}元</div>
            <div v-if="detailData.un_sign_type == 2 && detailData.customer_un_sign == 1" class="value">
              (单期 {{ detailData.violate_single_amount }}元)
            </div>
            <div v-if="detailData.un_sign_type == 1 && detailData.customer_un_sign == 0" class="value">线下收取</div>
            <div v-if="detailData.un_sign_type == 2 && detailData.customer_un_sign == 0" class="value">
              单期 {{ detailData.violate_amount }}元
            </div>
          </div>
        </div>
        <div v-if="detailData.coupon_amount !== '0.00'" class="line">
          <div class="item">
            <div class="label">抵扣券抵扣:</div>
            <div class="value">￥{{ detailData.coupon_amount }}</div>
          </div>
        </div>
        <div class="actions">
          <!-- <Button @click="handleDetailClose">关闭</Button> -->
          <!-- <Button v-if="detailData.status === 'NORMAL'" type="warning" style="margin-left: 20px"
            @click="handlePause(detailData.open_merchant_id, detailData.subscription_no)">暂停</Button>
          <Button v-if="detailData.status === 'PAUSED'" type="primary" style="margin-left: 20px"
            @click="handleRecover">恢复</Button> -->
        </div>
      </div>
      <template #footer>
        <a-button @click="handleDetailClose">关闭</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import Big from 'big.js';
  import { getContractsDetail } from '@/api/meituan';

  const props = defineProps({
    showModal: { type: Boolean, default: false },
    params: {
      type: Object,
      default: () => ({ bus_id: '', subscription_no: '', out_subscription_no: '', card_type: '' }),
    },
  });
  const emit = defineEmits(['update:showModal']);

  const detailModal = ref(false);
  const detailData = reactive<any>({});
  const normalTotal = ref<number | string>(0);
  const combinationsTotal = ref<number | string>(0);
  const combinations = ref<any[]>([]);

  const statusLabel = computed(() => {
    const m: any = {
      NORMAL: '(履约中)',
      PAUSED: '(已暂停)',
      SURRENDER: '(已解约)',
      END: '(已完成)',
      WAIT_SURRENDER: '(待解约)',
    };
    return m[detailData.status] || '';
  });

  function resetDetailData() {
    Object.keys(detailData).forEach((k) => delete detailData[k]);
    normalTotal.value = 0;
    combinationsTotal.value = 0;
    combinations.value = [];
  }

  async function getDetailData(bus_id: any, subscription_no = '', out_subscription_no = '', card_type = '') {
    const { data } = await getContractsDetail().execute({
      data: { bus_id, subscription_no, out_subscription_no, card_type },
    });
    if (data.value) {
      Object.assign(detailData, data.value);
      detailData.down_payment = Number(detailData.down_payment || 0).toFixed(2);
      detailData.deduction_amount = Number(detailData.deduction_amount || 0).toFixed(2);
      detailData.coupon_amount = Number(detailData.coupon_amount || 0).toFixed(2);
      detailData.violate_amount = Number(detailData.violate_amount || 0).toFixed(2);
      if (Number(detailData.product_type) === 2) {
        const combinationsArr = [];
        const combineListCopy = [...detailData.combine_list];
        combineListCopy.forEach((item) => {
          item.amount = Number(item.amount).toFixed(2);
        });
        if (Array.isArray(combineListCopy)) {
          const len = combineListCopy.length;
          const count = Math.ceil(len / 2);
          for (let i = 0; i < count; i += 1) {
            const one = combineListCopy[2 * i] || '';
            const two = combineListCopy[2 * i + 1] || '';
            combinationsArr.push([one, two]);
          }
          combinationsTotal.value = combineListCopy.reduce((total, item) => total + Number(item.amount), 0).toFixed(2);
        }
        combinations.value = combinationsArr;
        normalTotal.value = new Big(detailData.deduction_amount).times(Number(detailData.periods) || 0).toFixed(2);
      } else {
        normalTotal.value = new Big(detailData.deduction_amount).times(Number(detailData.periods) - 1 || 0).toFixed(2);
      }
      detailData.violate_single_amount = new Big(detailData.violate_amount || 0)
        .div(detailData.violate_periods || 1)
        .toFixed(2);

      if (Number(detailData?.product_info?.card_type_id) === 1) detailData.unit = '天';
      else if (Number(detailData?.product_info?.card_type_id) === 2) detailData.unit = '次';
      else if (Number(detailData?.product_info?.card_type_id) === 3) detailData.unit = '元';
      else if (Number(detailData?.product_info?.card_type_id) === 4) detailData.unit = '节';
      else if (Number(detailData?.product_info?.card_type_id) === 5) detailData.unit = '节';
      if (Number(detailData?.product_info?.is_pt_time_limit_card) === 1) detailData.unit = '天';

      detailModal.value = true;
    }
  }

  watch(
    () => props.showModal,
    (val) => {
      if (val) {
        resetDetailData();
        getDetailData(
          props.params.bus_id,
          props.params.subscription_no,
          props.params.out_subscription_no,
          props.params.card_type
        );
      } else {
        resetDetailData();
        detailModal.value = false;
      }
    }
  );

  function handleDetailClose() {
    resetDetailData();
    emit('update:showModal', false);
  }
</script>

<style lang="less" scoped>
  .one-line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .information {
    padding: 40px;

    .line {
      justify-content: space-between;
      min-height: 40px;
      .one-line;

      .item {
        min-width: 280px;
        .one-line;
      }

      .label {
        font-size: 16px;
        color: #999;
        width: 110px;
        text-align: right;
        text-wrap: nowrap;
      }

      .value {
        font-size: 16px;
        color: #333;
        margin-left: 10px;
        text-wrap: nowrap;
      }
    }

    .actions {
      margin: 20px auto 0 auto;
      width: 400px;
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
    }
  }
</style>
