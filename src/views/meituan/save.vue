<template>
  <div class="base-box">
    <Breadcrumb :items="['美团跑路赔', '跑路赔产品管理', props.id ? '编辑' : '新增']" />
    <a-card class="general-card">
      <!-- 提示信息 -->
      <a-alert type="info" style="margin-bottom: 24px" show-icon closable>
        美团跑路赔的月卡是否需要签署合同，您可以在 “设置-合同签署-美团跑路赔” 进行设置。
      </a-alert>

      <!-- 表单 -->
      <a-form ref="formRef" :model="formData" :rules="formRules" class="general-form" auto-label-width>
        <!-- 场馆选择 -->
        <a-form-item field="bus_id" label="场馆" required>
          <BusSelectAdmin
            v-model="formData.bus_id"
            style="width: 400px"
            placeholder="请选择场馆"
            @change="handleBusChange" />
        </a-form-item>

        <!-- 会籍卡选择 -->
        <a-form-item field="card_id" label="会籍卡" required>
          <div>
            <a-select
              v-model="formData.card_id"
              placeholder="请选择"
              allow-search
              allow-clear
              style="width: 400px"
              :loading="cardLoading">
              <a-option v-for="item in cardList" :key="item.id" :value="item.id">
                {{ item.name }}
              </a-option>
            </a-select>
            <div v-if="cardTip" class="form-tip">
              {{ cardTip }}
            </div>
          </div>
        </a-form-item>

        <!-- 美团跑路赔产品ID -->
        <a-form-item field="deal_id" label="美团跑路赔产品ID" required>
          <a-input
            v-model="formData.deal_id"
            placeholder="点击选择团购套餐"
            readonly
            allow-clear
            style="width: 400px; cursor: pointer"
            @click="openProductModal" />
        </a-form-item>

        <!-- 操作按钮 -->
        <a-form-item>
          <a-space>
            <a-button type="primary" :loading="submitLoading" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="handleCancel">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <!-- 产品选择模态框 -->
      <MeipingGoodsModal
        v-model="showProductModal"
        :platform-id="platformId"
        :bus-id="formData.bus_id"
        :selected-id="selectedId"
        @confirm="handleConfirmCoupon"
        @cancel="handleCloseCoupon"
        @auth="handleNeedAuth" />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { saveProductConfig, getCompensationDetail } from '@/api/meituan';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import { getCardCoupon } from '@/api/card-rule';
  import MeipingGoodsModal from '@/views/third-party/components/meiping-goods-modal.vue';

  defineOptions({
    name: 'MeituanSave',
  });

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
  });

  const router = useRouter();

  // 表单数据
  const formData = reactive({
    bus_id: '',
    card_id: '',
    deal_id: '',
  });

  // 表单验证规则
  const formRules = {
    bus_id: {
      required: true,
      message: '请选择场馆',
    },
    card_id: {
      required: true,
      message: '请选择会籍卡',
    },
    deal_id: {
      required: true,
      message: '请输入美团跑路赔产品ID',
    },
  };

  // 响应式数据
  const formRef = ref();
  const submitLoading = ref(false);
  const loading = ref(false);
  const cardLoading = ref(false);
  const cardList = ref<any[]>([]);
  const cardTip = ref('');

  const selectedId = ref(0);

  // 获取会员卡列表
  const getCardList = async (busId: string) => {
    if (!busId) {
      cardList.value = [];
      return;
    }

    cardLoading.value = true;
    try {
      const { data } = await getCardCoupon({
        bus_id: formData.bus_id,
        rule_type: 1, // 仅获取会籍卡
        number: 30, // 有效期30天
      });

      if (data.value && data.value.length > 0) {
        cardList.value = data.value;
        // 过滤出有效期为30天的会籍卡
        // const validCards = cardList.value.filter((card) => card.valid_days === 30 || card.valid_days === '30');
        const validCards = cardList.value; // 暂时不做过滤, 后端控制

        if (validCards.length > 0) {
          cardTip.value = `仅支持有效期为30天的会籍卡`;
        } else {
          cardTip.value = '当前场馆暂无有效期为30天的会籍卡';
        }
      } else {
        cardList.value = [];
        cardTip.value = '当前场馆暂无可用会籍卡';
      }
    } catch (error) {
      console.error('获取会员卡列表失败:', error);
      Message.error('获取会员卡列表失败');
      cardList.value = [];
      cardTip.value = '';
    } finally {
      cardLoading.value = false;
    }
  };

  // 场馆变化处理
  const handleBusChange = (busId: any) => {
    formData.card_id = '';
    getCardList(String(busId));
  };

  // 如果是编辑模式，加载详情
  const loadDetail = async (id: string) => {
    if (!id) return;
    loading.value = true;
    try {
      const { data } = await getCompensationDetail().execute({ data: { id } });
      if (data && data.value) {
        const v = data.value;
        formData.bus_id = v.bus_id || '';
        formData.card_id = v.card_id || '';
        formData.deal_id = v.deal_id || '';
        // load card list for the bus
        if (formData.bus_id) getCardList(String(formData.bus_id));
        // set selected product id
        selectedId.value = Number(v.deal_id) || 0;
      } else {
        Message.error('加载详情失败');
      }
    } catch (err) {
      console.error('获取详情失败:', err);
      Message.error('获取详情失败');
    } finally {
      loading.value = false;
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const valid = await formRef.value.validate();
      if (!valid) {
        submitLoading.value = true;

        formData.deal_id = selectedId.value.toString();

        // 调用保存API
        await saveProductConfig().execute({
          data: formData,
        });

        Message.success('保存成功');
        router.back();
      }
    } catch (error: any) {
      console.error('提交失败:', error);
      Message.error(error.message || '提交失败，请重试');
    } finally {
      submitLoading.value = false;
    }
  };

  // 取消操作
  const handleCancel = () => {
    router.back();
  };

  // 产品选择模态框逻辑
  const platformId = ref(1);
  const showProductModal = ref(false);

  function openProductModal() {
    // fetch products (demo data for now)
    showProductModal.value = true;
  }

  const handleNeedAuth = () => {
    Message.warning('请先授权对应的美团商户');
  };
  const handleConfirmCoupon = (deal_id: number) => {
    selectedId.value = deal_id;
    formData.deal_id = String(deal_id);
  };
  const handleCloseCoupon = () => {
    selectedId.value = 0;
  };

  // watch props.id to load detail when editing
  watch(
    () => props.id,
    (newId) => {
      if (newId) loadDetail(newId);
    },
    { immediate: true }
  );
</script>

<style lang="less" scoped>
  .form-tip {
    margin-top: 4px;
    font-size: 12px;
    color: #86909c;
    line-height: 1.5;
  }

  :deep(.arco-form-item-label) {
    font-weight: 500;
  }

  :deep(.arco-alert) {
    border-radius: 6px;
  }

  /* Modal title styling */
  .modal-title {
    text-align: center;
  }
  .modal-title-main {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 6px;
  }
  .modal-title-sub {
    font-size: 12px;
    color: #666;
  }
</style>
