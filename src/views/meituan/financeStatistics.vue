<template>
  <div class="finance-statistics page-container">
    <Breadcrumb />
    <a-card class="general-card">
      <div class="controls">
        <a-select v-model="selectedYear" style="width: 200px; margin-right: 10px" placeholder="请选择年份">
          <a-option v-for="y in years" :key="y" :value="y">{{ y }}年</a-option>
        </a-select>

        <BusSelectAdmin v-model="selectedVenues" style="width: 400px" placeholder="请选择场馆" multiple />
      </div>

      <div class="table-wrap">
        <table class="stats-table">
          <thead>
            <tr>
              <th>月份</th>
              <th>扣款成功订单总数量</th>
              <th>扣款成功总金额(¥)</th>
              <th>美团佣金(%)</th>
              <th>美团佣金总金额(¥)</th>
              <th>实际收入总金额(¥)</th>
              <th>扣款失败订单总数量</th>
              <th>扣款失败总金额(¥)</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(m, idx) in monthsData" :key="idx">
              <td class="month">{{ m.label }}</td>
              <td class="num">{{ formatNumber(m.successCount) }}</td>
              <td class="num">{{ formatCurrency(m.successAmount) }}</td>
              <td class="percent">{{ formatPercent(commissionPercent) }}</td>
              <td class="num">{{ formatCurrency((m.successAmount * commissionPercent) / 100) }}</td>
              <td class="num">{{ formatCurrency(m.successAmount - (m.successAmount * commissionPercent) / 100) }}</td>
              <td class="num">{{ formatNumber(m.failCount) }}</td>
              <td class="num">{{ formatCurrency(m.failAmount) }}</td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td>合计</td>
              <td class="num">{{ totals.successCount }}</td>
              <td class="num">{{ formatCurrency(totals.successAmount) }}</td>
              <td class="percent">--</td>
              <td class="num">{{ formatCurrency(totals.commissionAmount) }}</td>
              <td class="num">{{ formatCurrency(totals.actualIncome) }}</td>
              <td class="num">{{ totals.failCount }}</td>
              <td class="num">{{ formatCurrency(totals.failAmount) }}</td>
            </tr>
          </tfoot>
        </table>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, computed, watch } from 'vue';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';

  const now = new Date();
  const currentYear = now.getFullYear();

  const years = Array.from({ length: 6 }).map((_, i) => currentYear - (5 - i));
  const selectedYear = ref(currentYear);

  const selectedVenues = ref('');

  const commissionPercent = ref(2.5); // default as in screenshot

  // create 12 months rows with demo/empty values
  const months = Array.from({ length: 12 }).map((_, i) => ({ label: `${i + 1}月` }));

  const monthsData = reactive(
    months.map((m) => ({
      label: m.label,
      successCount: 0,
      successAmount: 0,
      failCount: 0,
      failAmount: 0,
    }))
  );

  // Totals computed
  const totals = computed(() => {
    const successCount = monthsData.reduce((s, m) => s + (m.successCount || 0), 0);
    const successAmount = monthsData.reduce((s, m) => s + (m.successAmount || 0), 0);
    const failCount = monthsData.reduce((s, m) => s + (m.failCount || 0), 0);
    const failAmount = monthsData.reduce((s, m) => s + (m.failAmount || 0), 0);
    const commissionAmount = successAmount * (commissionPercent.value / 100);
    const actualIncome = successAmount - commissionAmount;
    return {
      successCount,
      successAmount,
      failCount,
      failAmount,
      commissionAmount,
      actualIncome,
    };
  });

  // Demo fetch: replace with actual API call; generates zeros so table looks empty like screenshot
  function fetchData() {
    // For now, reset to zeros (keeps UI predictable). Uncomment to generate demo numbers.
    monthsData.forEach((m) => {
      m.successCount = 0;
      m.successAmount = 0;
      m.failCount = 0;
      m.failAmount = 0;
    });

    // Example: to show one sample value (matches screenshot's 2.50 percent shown in one cell)
    // monthsData[0].successAmount = 0
  }

  // initial load
  fetchData();

  watch([selectedYear, selectedVenues], () => {
    // When user changes filters we would re-fetch.
    fetchData();
  });

  function formatCurrency(v: number) {
    if (v === null || v === undefined) return '--';
    return v === 0 ? '' : v.toFixed(2);
  }

  function formatNumber(v: number) {
    if (v === null || v === undefined) return '--';
    return v === 0 ? '' : String(v);
  }

  function formatPercent(v: number) {
    if (v === null || v === undefined) return '--';
    return v === 0 ? '' : v.toFixed(2);
  }
</script>

<style lang="less" scoped>
  .page-container {
    padding: 12px;
  }
  .controls {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }
  .table-wrap {
    overflow: auto;
    border: 1px solid #e6e6e6;
  }
  .stats-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1100px;
  }
  .stats-table th,
  .stats-table td {
    border: 1px solid #dcdcdc;
    padding: 12px 10px;
    text-align: center;
    background: #fff;
  }
  .stats-table thead th {
    background: #fafafa;
    font-weight: 600;
  }
  .month {
    text-align: left;
    padding-left: 18px;
  }
  .percent {
    color: #1890ff;
  }
  .num {
    white-space: nowrap;
  }
  tfoot td {
    font-weight: 600;
  }
</style>
