{"name": "web-fe-v3", "version": "3.0.0", "private": true, "author": "Rocketbird Team", "scripts": {"dev": "vite --config ./config/vite.config.dev.ts", "build": "vite build --config ./config/vite.config.prod.ts", "cdn": "vite build --config ./config/vite.config.cdn.ts", "report": "cross-env REPORT=true npm run build", "preview": "npm run build && vite preview --host", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint-staged": "npx lint-staged"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix", "prettier --write"], "*.vue": ["prettier --write", "eslint --fix"], "*.{less,css}": ["prettier --write"]}, "dependencies": {"@arco-design/web-vue": "^2.55.2", "@arco-themes/vue-rocketbird": "^0.0.6", "@icon-park/vue-next": "^1.4.2", "@vueuse/core": "^10.11.0", "@vueuse/integrations": "^10.11.0", "@zxing/library": "^0.21.2", "ali-oss": "^6.20.0", "axios": "^1.6.8", "big.js": "^6.2.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.5", "echarts": "^5.4.0", "lodash": "^4.17.21", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.23", "qiankun": "^2.10.11", "qs": "^6.11.2", "query-string": "^8.0.3", "quill": "^1.3.7", "sortablejs": "^1.15.0", "vue": "^3.3.4", "vue-cropper": "next", "vue-echarts": "^6.2.3", "vue-router": "^4.2.4", "vue3-treeselect": "^0.1.10", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@arco-plugins/vite-vue": "^1.4.5", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@types/big.js": "^6.2.2", "@types/crypto-js": "^4.1.1", "@types/lodash": "^4.14.186", "@types/mockjs": "^1.0.7", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.40.0", "@typescript-eslint/parser": "^5.40.0", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/babel-plugin-jsx": "^1.1.1", "consola": "^2.15.3", "cross-env": "^7.0.3", "cypress": "^14.1.0", "eslint": "^8.25.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.6.0", "husky": "^8.0.1", "less": "^4.2.0", "lint-staged": "^13.0.3", "mockjs": "^1.1.0", "postcss-px-to-viewport-update": "^1.2.0", "prettier": "^2.7.1", "rollup": "^3.9.1", "rollup-plugin-visualizer": "^5.8.2", "typescript": "^4.8.4", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.24.1", "vite": "^4.4.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-svg-loader": "^3.6.0", "vue-tsc": "^1.8.8"}, "engines": {"node": ">=14.0.0"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3", "gifsicle": "5.2.0"}}